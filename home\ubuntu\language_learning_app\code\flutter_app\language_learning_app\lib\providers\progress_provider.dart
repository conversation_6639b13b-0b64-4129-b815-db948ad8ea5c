import 'package:flutter/material.dart';

class ProgressProvider extends ChangeNotifier {
  int _points = 0;
  int _coins = 0;
  int _streak = 0;
  int _hearts = 5;
  final int _maxHearts = 5;
  int _dailyGoal = 30;
  int _dailyProgress = 0;
  Map<String, double> _skillProgress = {};
  List<Map<String, dynamic>> _achievements = [];
  final List<Map<String, dynamic>> _completedLessons = [];

  // Getters
  int get points => _points;
  int get coins => _coins;
  int get streak => _streak;
  int get hearts => _hearts;
  int get maxHearts => _maxHearts;
  int get dailyGoal => _dailyGoal;
  int get dailyProgress => _dailyProgress;
  Map<String, double> get skillProgress => _skillProgress;
  List<Map<String, dynamic>> get achievements => _achievements;
  List<Map<String, dynamic>> get completedLessons => _completedLessons;

  // Initialize with default values
  ProgressProvider() {
    _initializeSkillProgress();
    _initializeAchievements();
  }

  void _initializeSkillProgress() {
    _skillProgress = {
      'listening': 0.2,
      'speaking': 0.1,
      'reading': 0.3,
      'writing': 0.15,
    };
  }

  void _initializeAchievements() {
    _achievements = [
      {
        'id': 'first_lesson',
        'title': 'أول درس',
        'description': 'أكملت أول درس لك',
        'icon': 'assets/icons/achievement_first_lesson.png',
        'unlocked': true,
      },
      {
        'id': 'three_day_streak',
        'title': 'تتابع لمدة 3 أيام',
        'description': 'تعلمت لمدة 3 أيام متتالية',
        'icon': 'assets/icons/achievement_streak.png',
        'unlocked': false,
      },
      {
        'id': 'vocabulary_master',
        'title': 'سيد المفردات',
        'description': 'تعلمت 50 كلمة جديدة',
        'icon': 'assets/icons/achievement_vocabulary.png',
        'unlocked': false,
      },
      {
        'id': 'perfect_score',
        'title': 'درجة كاملة',
        'description': 'حصلت على درجة كاملة في تمرين',
        'icon': 'assets/icons/achievement_perfect.png',
        'unlocked': false,
      },
      {
        'id': 'first_conversation',
        'title': 'أول محادثة',
        'description': 'شاركت في أول محادثة جماعية',
        'icon': 'assets/icons/achievement_conversation.png',
        'unlocked': false,
      },
    ];
  }

  // Methods to update progress
  void addPoints(int amount) {
    _points += amount;
    _dailyProgress += amount;
    notifyListeners();

    // Check if daily goal is reached
    if (_dailyProgress >= _dailyGoal && _dailyProgress - amount < _dailyGoal) {
      _addCoins(10); // Bonus coins for reaching daily goal
    }
  }

  void _addCoins(int amount) {
    _coins += amount;
    notifyListeners();
  }

  bool spendCoins(int amount) {
    if (_coins >= amount) {
      _coins -= amount;
      notifyListeners();
      return true;
    }
    return false;
  }

  void incrementStreak() {
    _streak++;
    notifyListeners();

    // Check for streak achievements
    if (_streak == 3) {
      unlockAchievement('three_day_streak');
    } else if (_streak == 7) {
      unlockAchievement('seven_day_streak');
    } else if (_streak == 30) {
      unlockAchievement('thirty_day_streak');
    }
  }

  void resetStreak() {
    _streak = 0;
    notifyListeners();
  }

  void useHeart() {
    if (_hearts > 0) {
      _hearts--;
      notifyListeners();
    }
  }

  void refillHearts() {
    _hearts = _maxHearts;
    notifyListeners();
  }

  void addHeart() {
    if (_hearts < _maxHearts) {
      _hearts++;
      notifyListeners();
    }
  }

  void setDailyGoal(int goal) {
    if (goal > 0) {
      _dailyGoal = goal;
      notifyListeners();
    }
  }

  void resetDailyProgress() {
    _dailyProgress = 0;
    notifyListeners();
  }

  void updateSkillProgress(String skill, double progress) {
    if (_skillProgress.containsKey(skill)) {
      // Ensure progress is between 0 and 1
      progress = progress.clamp(0.0, 1.0);
      _skillProgress[skill] = progress;
      notifyListeners();
    }
  }

  void unlockAchievement(String achievementId) {
    final achievementIndex =
        _achievements.indexWhere((a) => a['id'] == achievementId);
    if (achievementIndex != -1 &&
        _achievements[achievementIndex]['unlocked'] == false) {
      _achievements[achievementIndex]['unlocked'] = true;
      _addCoins(20); // Reward for unlocking achievement
      notifyListeners();
    }
  }

  void completeLesson(
      String lessonId, String lessonTitle, int score, int maxScore) {
    // Check if lesson is already completed
    final existingIndex =
        _completedLessons.indexWhere((l) => l['id'] == lessonId);

    if (existingIndex != -1) {
      // Update existing lesson if new score is higher
      if (score > _completedLessons[existingIndex]['score']) {
        _completedLessons[existingIndex]['score'] = score;
        _completedLessons[existingIndex]['completion_date'] =
            DateTime.now().toIso8601String();
      }
    } else {
      // Add new completed lesson
      _completedLessons.add({
        'id': lessonId,
        'title': lessonTitle,
        'score': score,
        'max_score': maxScore,
        'completion_date': DateTime.now().toIso8601String(),
      });

      // If this is the first lesson, unlock achievement
      if (_completedLessons.length == 1) {
        unlockAchievement('first_lesson');
      }

      // If perfect score, unlock achievement
      if (score == maxScore) {
        unlockAchievement('perfect_score');
      }
    }

    // Add points based on score
    addPoints(score);

    notifyListeners();
  }

  // Get learning statistics
  Map<String, dynamic> getLearningStats() {
    int totalLessonsCompleted = _completedLessons.length;
    int totalPoints = _points;
    int vocabularyLearned =
        totalLessonsCompleted * 5; // Estimate: 5 words per lesson

    return {
      'total_lessons_completed': totalLessonsCompleted,
      'total_points': totalPoints,
      'vocabulary_learned': vocabularyLearned,
      'streak': _streak,
      'skill_progress': _skillProgress,
    };
  }

  // Get leaderboard data (would be replaced with actual API call)
  Future<List<Map<String, dynamic>>> getLeaderboard() async {
    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 800));

    // Sample leaderboard data
    return [
      {'user_id': 'user_456', 'name': 'أحمد', 'points': 1250, 'rank': 1},
      {'user_id': 'user_789', 'name': 'سارة', 'points': 980, 'rank': 2},
      {
        'user_id': 'user_123',
        'name': 'مستخدم جديد',
        'points': _points,
        'rank': 3
      },
      {'user_id': 'user_234', 'name': 'محمد', 'points': 720, 'rank': 4},
      {'user_id': 'user_345', 'name': 'فاطمة', 'points': 650, 'rank': 5},
    ];
  }
}
