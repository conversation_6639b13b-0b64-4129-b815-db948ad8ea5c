import 'package:flutter/material.dart';
import 'package:language_learning_app/providers/auth_provider.dart';
import 'package:language_learning_app/providers/progress_provider.dart';
import 'package:language_learning_app/theme/app_theme.dart';
import 'package:language_learning_app/widgets/custom_button.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final progressProvider = Provider.of<ProgressProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              // Navigate to edit profile screen
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 20),
            // Profile Picture
            CircleAvatar(
              radius: 60,
              backgroundColor: AppTheme.backgroundSecondaryColor,
              backgroundImage: authProvider.userProfileImage != null
                  ? CachedNetworkImageProvider(authProvider.userProfileImage!)
                  : null,
              child: authProvider.userProfileImage == null
                  ? const Icon(
                      Icons.person,
                      size: 60,
                      color: AppTheme.textSecondaryColor,
                    )
                  : null,
            ),
            const SizedBox(height: 16),
            // User Name
            Text(
              authProvider.userName ?? 'مستخدم',
              style: AppTheme.headingMedium,
            ),
            const SizedBox(height: 8),
            // User Email
            Text(
              authProvider.userEmail ?? '<EMAIL>',
              style: AppTheme.bodyMedium
                  .copyWith(color: AppTheme.textSecondaryColor),
            ),
            const SizedBox(height: 32),
            // Stats Section
            _buildStatsSection(context, progressProvider),
            const SizedBox(height: 32),
            // Achievements Section
            _buildAchievementsSection(context, progressProvider),
            const SizedBox(height: 32),
            // Sign Out Button
            CustomButton(
              text: 'تسجيل الخروج',
              onPressed: () async {
                await authProvider.signOut();
                // Navigate back to Welcome/Login screen
                Navigator.of(context)
                    .pushNamedAndRemoveUntil('/welcome', (route) => false);
              },
              isPrimary: false,
              icon: Icons.logout,
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(
      BuildContext context, ProgressProvider progressProvider) {
    final stats = progressProvider.getLearningStats();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('إحصائيات التعلم', style: AppTheme.headingSmall),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('النقاط', stats['total_points']?.toString() ?? '0',
                Icons.star_border, AppTheme.accentColor),
            _buildStatItem('التتابع', stats['streak']?.toString() ?? '0',
                Icons.local_fire_department_outlined, AppTheme.accentColor),
            _buildStatItem(
                'الدروس',
                stats['total_lessons_completed']?.toString() ?? '0',
                Icons.menu_book_outlined,
                AppTheme.secondaryColor),
          ],
        ),
        const SizedBox(height: 24),
        Text('تقدم المهارات',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),
        ...(stats['skill_progress'] as Map<String, double>)
            .entries
            .map((entry) {
          return _buildSkillProgress(entry.key, entry.value);
        }).toList(),
      ],
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, size: 30, color: color),
        const SizedBox(height: 8),
        Text(value, style: AppTheme.headingSmall.copyWith(color: color)),
        const SizedBox(height: 4),
        Text(label, style: AppTheme.bodySmall),
      ],
    );
  }

  Widget _buildSkillProgress(String skillName, double progress) {
    String translatedSkillName = skillName;
    switch (skillName) {
      case 'listening':
        translatedSkillName = 'الاستماع';
        break;
      case 'speaking':
        translatedSkillName = 'التحدث';
        break;
      case 'reading':
        translatedSkillName = 'القراءة';
        break;
      case 'writing':
        translatedSkillName = 'الكتابة';
        break;
    }
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        children: [
          Expanded(
              child: Text(translatedSkillName, style: AppTheme.bodyMedium)),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: AppTheme.backgroundSecondaryColor,
              valueColor:
                  const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 8),
          Text('${(progress * 100).toInt()}%', style: AppTheme.bodySmall),
        ],
      ),
    );
  }

  Widget _buildAchievementsSection(
      BuildContext context, ProgressProvider progressProvider) {
    final unlockedAchievements = progressProvider.achievements
        .where((a) => a['unlocked'] == true)
        .toList();
    final lockedAchievements = progressProvider.achievements
        .where((a) => a['unlocked'] == false)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('الإنجازات', style: AppTheme.headingSmall),
        const SizedBox(height: 16),
        if (unlockedAchievements.isEmpty && lockedAchievements.isEmpty)
          Center(
              child: Text('لا توجد إنجازات بعد.', style: AppTheme.bodyMedium))
        else
          SizedBox(
            height: 120, // Adjust height as needed
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                ...unlockedAchievements
                    .map((a) => _buildAchievementItem(a, true)),
                ...lockedAchievements
                    .map((a) => _buildAchievementItem(a, false)),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildAchievementItem(
      Map<String, dynamic> achievement, bool unlocked) {
    return Opacity(
      opacity: unlocked ? 1.0 : 0.5,
      child: Container(
        width: 100,
        margin: const EdgeInsets.symmetric(horizontal: 8.0),
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: AppTheme.backgroundSecondaryColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
              color: unlocked ? AppTheme.primaryColor : Colors.grey.shade300),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              achievement['icon'] ?? 'assets/icons/achievement_default.png',
              height: 40,
              width: 40,
              color: unlocked ? null : Colors.grey,
              errorBuilder: (context, error, stackTrace) => Icon(
                unlocked ? Icons.emoji_events_outlined : Icons.lock_outline,
                size: 40,
                color: unlocked ? AppTheme.accentColor : Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              achievement['title'] ?? 'إنجاز',
              style: AppTheme.bodySmall.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
