{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\Compressed\\\\flutter_windows_3.29.3-stable\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\Compressed\\\\flutter_windows_3.29.3-stable\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [], "linux": [], "windows": [], "web": []}, "dependencyGraph": [{"name": "integration_test", "dependencies": []}], "date_created": "2025-05-25 15:36:39.652472", "version": "3.29.3", "swift_package_manager_enabled": {"ios": false, "macos": false}}