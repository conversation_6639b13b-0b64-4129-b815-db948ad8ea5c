class User {
  final String id;
  final String email;
  final String name;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final List<String> learningLanguages;
  final String nativeLanguage;
  final int level;
  final int points;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.profileImageUrl,
    required this.createdAt,
    required this.lastLoginAt,
    required this.learningLanguages,
    required this.nativeLanguage,
    this.level = 1,
    this.points = 0,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      profileImageUrl: json['profileImageUrl'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] ?? DateTime.now().toIso8601String()),
      learningLanguages: List<String>.from(json['learningLanguages'] ?? []),
      nativeLanguage: json['nativeLanguage'] ?? 'en',
      level: json['level'] ?? 1,
      points: json['points'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'profileImageUrl': profileImageUrl,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'learningLanguages': learningLanguages,
      'nativeLanguage': nativeLanguage,
      'level': level,
      'points': points,
    };
  }

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? profileImageUrl,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    List<String>? learningLanguages,
    String? nativeLanguage,
    int? level,
    int? points,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      learningLanguages: learningLanguages ?? this.learningLanguages,
      nativeLanguage: nativeLanguage ?? this.nativeLanguage,
      level: level ?? this.level,
      points: points ?? this.points,
    );
  }
}
