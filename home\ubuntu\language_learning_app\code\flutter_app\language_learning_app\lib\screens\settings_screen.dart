import 'package:flutter/material.dart';
import 'package:language_learning_app/providers/settings_provider.dart';
import 'package:language_learning_app/theme/app_theme.dart';
import 'package:provider/provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final settingsProvider = Provider.of<SettingsProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        children: [
          _buildSectionTitle(context, 'إعدادات الحساب'),
          _buildSettingsGroup(
            context,
            children: [
              _buildNavigationSettingItem(context, 'تعديل الملف الشخصي', Icons.person_outline, () {
                // Navigate to edit profile screen
              }),
              _buildNavigationSettingItem(context, 'تغيير كلمة المرور', Icons.lock_outline, () {
                // Navigate to change password screen
              }),
              _buildNavigationSettingItem(context, 'حسابات التواصل الاجتماعي', Icons.link, () {
                // Navigate to linked accounts screen
              }),
              _buildSwitchSettingItem(
                context,
                'إشعارات البريد الإلكتروني',
                Icons.email_outlined,
                settingsProvider.saveLearnData, // Assuming this maps to email notifications for now
                (value) => settingsProvider.toggleSaveLearnData(value), // Update provider method if needed
              ),
            ],
          ),
          _buildSectionTitle(context, 'إعدادات التعلم'),
          _buildSettingsGroup(
            context,
            children: [
              _buildDropdownSettingItem(
                context,
                'هدف التعلم اليومي',
                Icons.track_changes,
                settingsProvider.dailyGoal.toString(),
                ['10', '20', '30', '50', '100'],
                (value) => settingsProvider.setDailyGoal(int.parse(value ?? '30')),
              ),
              _buildSwitchSettingItem(
                context,
                'تذكير يومي',
                Icons.notifications_outlined,
                settingsProvider.dailyReminder,
                settingsProvider.toggleDailyReminder,
              ),
              if (settingsProvider.dailyReminder)
                _buildTimeSettingItem(
                  context,
                  'وقت التذكير',
                  Icons.access_time,
                  settingsProvider.reminderTime,
                  (time) => settingsProvider.setReminderTime(time ?? const TimeOfDay(hour: 8, minute: 0)),
                ),
              _buildDropdownSettingItem(
                context,
                'مستوى الصعوبة',
                Icons.leaderboard_outlined,
                settingsProvider.difficultyLevel,
                ['سهل', 'متوسط', 'صعب'],
                (value) => settingsProvider.setDifficultyLevel(value ?? 'متوسط'),
              ),
              _buildSwitchSettingItem(
                context,
                'السماح بالأخطاء البسيطة',
                Icons.spellcheck,
                settingsProvider.allowMinorMistakes,
                settingsProvider.toggleAllowMinorMistakes,
              ),
            ],
          ),
          _buildSectionTitle(context, 'إعدادات التطبيق'),
          _buildSettingsGroup(
            context,
            children: [
              _buildDropdownSettingItem(
                context,
                'لغة التطبيق',
                Icons.language,
                _getLanguageName(settingsProvider.appLocale),
                ['العربية', 'English', 'Français', 'Español', 'Deutsch'], // Add more as needed
                (value) => settingsProvider.setAppLocale(_getLocaleFromName(value ?? 'العربية')),
              ),
              _buildSwitchSettingItem(
                context,
                'الصوت',
                Icons.volume_up_outlined,
                settingsProvider.soundEnabled,
                settingsProvider.toggleSound,
              ),
              if (settingsProvider.soundEnabled)
                _buildSliderSettingItem(
                  context,
                  'مستوى الصوت',
                  Icons.volume_down,
                  settingsProvider.soundVolume,
                  settingsProvider.setSoundVolume,
                ),
              _buildSwitchSettingItem(
                context,
                'الاهتزاز',
                Icons.vibration,
                settingsProvider.vibrationEnabled,
                settingsProvider.toggleVibration,
              ),
              _buildSwitchSettingItem(
                context,
                'وضع توفير البطارية',
                Icons.battery_saver_outlined,
                settingsProvider.batterySaverMode,
                settingsProvider.toggleBatterySaverMode,
              ),
              _buildSwitchSettingItem(
                context,
                'وضع عدم الاتصال',
                Icons.signal_wifi_off_outlined,
                settingsProvider.offlineMode,
                settingsProvider.toggleOfflineMode,
              ),
              if (settingsProvider.offlineMode)
                _buildNavigationSettingItem(context, 'تنزيل الدروس', Icons.download_outlined, () {
                  // Navigate to download lessons screen
                }),
            ],
          ),
          _buildSectionTitle(context, 'الخصوصية'),
          _buildSettingsGroup(
            context,
            children: [
              _buildSwitchSettingItem(
                context,
                'ملف شخصي عام',
                Icons.public_outlined,
                settingsProvider.publicProfile,
                settingsProvider.togglePublicProfile,
              ),
              _buildSwitchSettingItem(
                context,
                'إظهار التقدم للأصدقاء',
                Icons.group_outlined,
                settingsProvider.showProgressToFriends,
                settingsProvider.toggleShowProgressToFriends,
              ),
              _buildDropdownSettingItem(
                context,
                'السماح بالرسائل من',
                Icons.message_outlined,
                settingsProvider.messagePermission,
                ['الجميع', 'الأصدقاء فقط', 'لا أحد'],
                (value) => settingsProvider.setMessagePermission(value ?? 'الأصدقاء فقط'),
              ),
              _buildSwitchSettingItem(
                context,
                'حفظ بيانات التعلم',
                Icons.save_alt_outlined,
                settingsProvider.saveLearnData,
                settingsProvider.toggleSaveLearnData,
              ),
            ],
          ),
          _buildSectionTitle(context, 'الدعم والمساعدة'),
          _buildSettingsGroup(
            context,
            children: [
              _buildNavigationSettingItem(context, 'الأسئلة الشائعة', Icons.help_outline, () {}),
              _buildNavigationSettingItem(context, 'التواصل مع الدعم', Icons.support_agent_outlined, () {}),
              _buildNavigationSettingItem(context, 'تقديم اقتراحات', Icons.lightbulb_outline, () {}),
              _buildNavigationSettingItem(context, 'الإبلاغ عن مشكلة', Icons.report_problem_outlined, () {}),
              _buildNavigationSettingItem(context, 'تقييم التطبيق', Icons.star_outline, () {}),
            ],
          ),
          _buildSectionTitle(context, 'معلومات التطبيق'),
          _buildSettingsGroup(
            context,
            children: [
              _buildInfoSettingItem(context, 'إصدار التطبيق', '1.0.0', Icons.info_outline),
              _buildNavigationSettingItem(context, 'شروط الاستخدام', Icons.description_outlined, () {}),
              _buildNavigationSettingItem(context, 'سياسة الخصوصية', Icons.privacy_tip_outlined, () {}),
              _buildNavigationSettingItem(context, 'تراخيص المصادر المفتوحة', Icons.code_outlined, () {}),
            ],
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40.0),
            child: OutlinedButton.icon(
              icon: const Icon(Icons.logout, color: AppTheme.errorColor),
              label: const Text('تسجيل الخروج', style: TextStyle(color: AppTheme.errorColor)),
              onPressed: () async {
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.signOut();
                Navigator.of(context).pushNamedAndRemoveUntil('/welcome', (route) => false);
              },
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: AppTheme.errorColor),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
              ),
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Text(
        title,
        style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondaryColor, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildSettingsGroup(BuildContext context, {required List<Widget> children}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSettingItemBase(BuildContext context, String title, IconData icon, Widget? trailing, VoidCallback? onTap) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.textSecondaryColor, size: 24),
      title: Text(title, style: AppTheme.bodyMedium),
      trailing: trailing,
      onTap: onTap,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
    );
  }

  Widget _buildSwitchSettingItem(BuildContext context, String title, IconData icon, bool value, Function(bool) onChanged) {
    return _buildSettingItemBase(
      context,
      title,
      icon,
      Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryColor,
      ),
      () => onChanged(!value), // Allow tapping the row to toggle
    );
  }

  Widget _buildNavigationSettingItem(BuildContext context, String title, IconData icon, VoidCallback onTap) {
    return _buildSettingItemBase(
      context,
      title,
      icon,
      const Icon(Icons.arrow_forward_ios, size: 16, color: AppTheme.textSecondaryColor),
      onTap,
    );
  }

  Widget _buildInfoSettingItem(BuildContext context, String title, String value, IconData icon) {
    return _buildSettingItemBase(
      context,
      title,
      icon,
      Text(value, style: AppTheme.bodySmall),
      null,
    );
  }

  Widget _buildDropdownSettingItem(BuildContext context, String title, IconData icon, String currentValue, List<String> options, Function(String?) onChanged) {
    return _buildSettingItemBase(
      context,
      title,
      icon,
      DropdownButton<String>(
        value: currentValue,
        items: options.map((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(value, style: AppTheme.bodyMedium),
          );
        }).toList(),
        onChanged: onChanged,
        underline: Container(), // Remove underline
        isDense: true,
        icon: const Icon(Icons.arrow_drop_down, color: AppTheme.textSecondaryColor),
      ),
      null,
    );
  }

  Widget _buildSliderSettingItem(BuildContext context, String title, IconData icon, double value, Function(double) onChanged) {
    return Column(
      children: [
        _buildSettingItemBase(
          context,
          title,
          icon,
          Text('${(value * 100).toInt()}%', style: AppTheme.bodySmall),
          null,
        ),
        Slider(
          value: value,
          onChanged: onChanged,
          min: 0.0,
          max: 1.0,
          activeColor: AppTheme.secondaryColor,
          inactiveColor: AppTheme.backgroundSecondaryColor,
        ),
      ],
    );
  }

  Widget _buildTimeSettingItem(BuildContext context, String title, IconData icon, TimeOfDay currentTime, Function(TimeOfDay?) onTimeChanged) {
    return _buildSettingItemBase(
      context,
      title,
      icon,
      Text(currentTime.format(context), style: AppTheme.bodyMedium.copyWith(color: AppTheme.secondaryColor)),
      () async {
        final TimeOfDay? picked = await showTimePicker(
          context: context,
          initialTime: currentTime,
        );
        if (picked != null) {
          onTimeChanged(picked);
        }
      },
    );
  }

  String _getLanguageName(Locale locale) {
    switch (locale.languageCode) {
      case 'ar': return 'العربية';
      case 'en': return 'English';
      case 'fr': return 'Français';
      case 'es': return 'Español';
      case 'de': return 'Deutsch';
      default: return locale.languageCode;
    }
  }

  Locale _getLocaleFromName(String name) {
    switch (name) {
      case 'العربية': return const Locale('ar', '');
      case 'English': return const Locale('en', '');
      case 'Français': return const Locale('fr', '');
      case 'Español': return const Locale('es', '');
      case 'Deutsch': return const Locale('de', '');
      default: return const Locale('ar', '');
    }
  }
}
