import 'package:flutter/material.dart';

class SettingsProvider extends ChangeNotifier {
  // App language settings
  Locale _appLocale = const Locale('ar', '');
  
  // Learning settings
  int _dailyGoal = 30;
  bool _dailyReminder = true;
  TimeOfDay _reminderTime = const TimeOfDay(hour: 8, minute: 0);
  String _difficultyLevel = 'متوسط';
  bool _allowMinorMistakes = true;
  
  // App settings
  bool _soundEnabled = true;
  double _soundVolume = 0.7;
  bool _vibrationEnabled = true;
  bool _batterySaverMode = false;
  bool _offlineMode = false;
  
  // Privacy settings
  bool _publicProfile = true;
  bool _showProgressToFriends = true;
  String _messagePermission = 'الأصدقاء فقط';
  bool _saveLearnData = true;
  
  // Getters
  Locale get appLocale => _appLocale;
  int get dailyGoal => _dailyGoal;
  bool get dailyReminder => _dailyReminder;
  TimeOfDay get reminderTime => _reminderTime;
  String get difficultyLevel => _difficultyLevel;
  bool get allowMinorMistakes => _allowMinorMistakes;
  bool get soundEnabled => _soundEnabled;
  double get soundVolume => _soundVolume;
  bool get vibrationEnabled => _vibrationEnabled;
  bool get batterySaverMode => _batterySaverMode;
  bool get offlineMode => _offlineMode;
  bool get publicProfile => _publicProfile;
  bool get showProgressToFriends => _showProgressToFriends;
  String get messagePermission => _messagePermission;
  bool get saveLearnData => _saveLearnData;
  
  // Methods
  void setAppLocale(Locale locale) {
    _appLocale = locale;
    notifyListeners();
  }
  
  void setDailyGoal(int goal) {
    if (goal > 0) {
      _dailyGoal = goal;
      notifyListeners();
    }
  }
  
  void toggleDailyReminder(bool value) {
    _dailyReminder = value;
    notifyListeners();
  }
  
  void setReminderTime(TimeOfDay time) {
    _reminderTime = time;
    notifyListeners();
  }
  
  void setDifficultyLevel(String level) {
    if (['سهل', 'متوسط', 'صعب'].contains(level)) {
      _difficultyLevel = level;
      notifyListeners();
    }
  }
  
  void toggleAllowMinorMistakes(bool value) {
    _allowMinorMistakes = value;
    notifyListeners();
  }
  
  void toggleSound(bool value) {
    _soundEnabled = value;
    notifyListeners();
  }
  
  void setSoundVolume(double volume) {
    if (volume >= 0 && volume <= 1) {
      _soundVolume = volume;
      notifyListeners();
    }
  }
  
  void toggleVibration(bool value) {
    _vibrationEnabled = value;
    notifyListeners();
  }
  
  void toggleBatterySaverMode(bool value) {
    _batterySaverMode = value;
    notifyListeners();
  }
  
  void toggleOfflineMode(bool value) {
    _offlineMode = value;
    notifyListeners();
  }
  
  void togglePublicProfile(bool value) {
    _publicProfile = value;
    notifyListeners();
  }
  
  void toggleShowProgressToFriends(bool value) {
    _showProgressToFriends = value;
    notifyListeners();
  }
  
  void setMessagePermission(String permission) {
    if (['الجميع', 'الأصدقاء فقط', 'لا أحد'].contains(permission)) {
      _messagePermission = permission;
      notifyListeners();
    }
  }
  
  void toggleSaveLearnData(bool value) {
    _saveLearnData = value;
    notifyListeners();
  }
  
  // Save settings to local storage
  Future<void> saveSettings() async {
    // This would be replaced with actual storage logic
    await Future.delayed(const Duration(milliseconds: 300));
    // Success would be returned here
  }
  
  // Load settings from local storage
  Future<void> loadSettings() async {
    // This would be replaced with actual storage logic
    await Future.delayed(const Duration(milliseconds: 300));
    // Settings would be loaded here
    notifyListeners();
  }
  
  // Reset settings to defaults
  void resetToDefaults() {
    _appLocale = const Locale('ar', '');
    _dailyGoal = 30;
    _dailyReminder = true;
    _reminderTime = const TimeOfDay(hour: 8, minute: 0);
    _difficultyLevel = 'متوسط';
    _allowMinorMistakes = true;
    _soundEnabled = true;
    _soundVolume = 0.7;
    _vibrationEnabled = true;
    _batterySaverMode = false;
    _offlineMode = false;
    _publicProfile = true;
    _showProgressToFriends = true;
    _messagePermission = 'الأصدقاء فقط';
    _saveLearnData = true;
    
    notifyListeners();
  }
}
