import 'package:flutter/material.dart';
import 'package:language_learning_app/theme/app_theme.dart';
import 'package:language_learning_app/screens/welcome_screen.dart';
import 'package:provider/provider.dart';
import 'package:language_learning_app/providers/auth_provider.dart';
import 'package:language_learning_app/providers/language_provider.dart';
import 'package:language_learning_app/providers/progress_provider.dart';
import 'package:language_learning_app/providers/settings_provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:language_learning_app/localization/app_localizations.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => ProgressProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return MaterialApp(
            title: 'تطبيق تعلم اللغات',
            theme: AppTheme.lightTheme,
            debugShowCheckedModeBanner: false,
            home: const WelcomeScreen(),
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar', ''), // Arabic
              Locale('en', ''), // English
              Locale('fr', ''), // French
              Locale('es', ''), // Spanish
              Locale('de', ''), // German
            ],
            locale: settingsProvider.appLocale,
          );
        },
      ),
    );
  }
}
