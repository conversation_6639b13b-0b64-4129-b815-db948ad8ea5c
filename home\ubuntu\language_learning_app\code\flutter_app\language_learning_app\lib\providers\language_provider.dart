import 'package:flutter/material.dart';

class LanguageProvider extends ChangeNotifier {
  List<String> _supportedLanguages = [
    'العربية',
    'English',
    'Français',
    'Español',
    'Deutsch',
    'Italiano',
    'Русский',
    '中文',
    '日本語',
    '한국어',
  ];
  
  String _selectedLearningLanguage = 'English';
  String _userNativeLanguage = 'العربية';
  int _currentLevel = 1;
  
  // Getters
  List<String> get supportedLanguages => _supportedLanguages;
  String get selectedLearningLanguage => _selectedLearningLanguage;
  String get userNativeLanguage => _userNativeLanguage;
  int get currentLevel => _currentLevel;
  
  // Methods
  void setLearningLanguage(String language) {
    if (_supportedLanguages.contains(language)) {
      _selectedLearningLanguage = language;
      notifyListeners();
    }
  }
  
  void setNativeLanguage(String language) {
    if (_supportedLanguages.contains(language)) {
      _userNativeLanguage = language;
      notifyListeners();
    }
  }
  
  void setCurrentLevel(int level) {
    if (level > 0) {
      _currentLevel = level;
      notifyListeners();
    }
  }
  
  // Get vocabulary for current level and language
  Future<List<Map<String, String>>> getVocabulary() async {
    // This would be replaced with actual API call or database query
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Sample vocabulary data
    if (_selectedLearningLanguage == 'English') {
      return [
        {'word': 'Hello', 'translation': 'مرحبا', 'example': 'Hello, how are you?'},
        {'word': 'Goodbye', 'translation': 'وداعا', 'example': 'Goodbye, see you tomorrow.'},
        {'word': 'Thank you', 'translation': 'شكرا لك', 'example': 'Thank you for your help.'},
        {'word': 'Please', 'translation': 'من فضلك', 'example': 'Please, can you help me?'},
        {'word': 'Yes', 'translation': 'نعم', 'example': 'Yes, I understand.'},
        {'word': 'No', 'translation': 'لا', 'example': 'No, I don\'t want that.'},
        {'word': 'Excuse me', 'translation': 'عذرا', 'example': 'Excuse me, where is the bathroom?'},
        {'word': 'Sorry', 'translation': 'آسف', 'example': 'I\'m sorry for being late.'},
        {'word': 'Water', 'translation': 'ماء', 'example': 'I would like some water, please.'},
        {'word': 'Food', 'translation': 'طعام', 'example': 'The food is delicious.'},
      ];
    } else if (_selectedLearningLanguage == 'Français') {
      return [
        {'word': 'Bonjour', 'translation': 'مرحبا', 'example': 'Bonjour, comment ça va?'},
        {'word': 'Au revoir', 'translation': 'وداعا', 'example': 'Au revoir, à demain.'},
        {'word': 'Merci', 'translation': 'شكرا لك', 'example': 'Merci pour votre aide.'},
        {'word': 'S\'il vous plaît', 'translation': 'من فضلك', 'example': 'S\'il vous plaît, pouvez-vous m\'aider?'},
        {'word': 'Oui', 'translation': 'نعم', 'example': 'Oui, je comprends.'},
        {'word': 'Non', 'translation': 'لا', 'example': 'Non, je ne veux pas ça.'},
        {'word': 'Excusez-moi', 'translation': 'عذرا', 'example': 'Excusez-moi, où sont les toilettes?'},
        {'word': 'Désolé', 'translation': 'آسف', 'example': 'Je suis désolé d\'être en retard.'},
        {'word': 'Eau', 'translation': 'ماء', 'example': 'Je voudrais de l\'eau, s\'il vous plaît.'},
        {'word': 'Nourriture', 'translation': 'طعام', 'example': 'La nourriture est délicieuse.'},
      ];
    } else {
      // Default to basic vocabulary for other languages
      return [
        {'word': 'Hello', 'translation': 'مرحبا', 'example': 'Basic greeting'},
        {'word': 'Goodbye', 'translation': 'وداعا', 'example': 'Basic farewell'},
        {'word': 'Thank you', 'translation': 'شكرا لك', 'example': 'Expression of gratitude'},
        {'word': 'Please', 'translation': 'من فضلك', 'example': 'Polite request'},
        {'word': 'Yes', 'translation': 'نعم', 'example': 'Affirmation'},
        {'word': 'No', 'translation': 'لا', 'example': 'Negation'},
      ];
    }
  }
  
  // Get grammar lessons for current level and language
  Future<List<Map<String, String>>> getGrammarLessons() async {
    // This would be replaced with actual API call or database query
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Sample grammar lessons
    if (_selectedLearningLanguage == 'English') {
      return [
        {
          'title': 'Present Simple Tense',
          'explanation': 'Used for habits, repeated actions, and general truths.',
          'example': 'I go to school every day. She works in a bank.'
        },
        {
          'title': 'Present Continuous Tense',
          'explanation': 'Used for actions happening now or around now.',
          'example': 'I am studying English. They are playing football.'
        },
        {
          'title': 'Articles (a, an, the)',
          'explanation': 'Used before nouns to indicate specificity.',
          'example': 'I saw a cat. The cat was black.'
        },
      ];
    } else if (_selectedLearningLanguage == 'Français') {
      return [
        {
          'title': 'Le Présent',
          'explanation': 'Utilisé pour les habitudes et les vérités générales.',
          'example': 'Je vais à l\'école tous les jours. Elle travaille dans une banque.'
        },
        {
          'title': 'Les Articles (le, la, les, un, une, des)',
          'explanation': 'Utilisés avant les noms pour indiquer le genre et le nombre.',
          'example': 'Le chat, la maison, les enfants, un livre, une pomme, des amis.'
        },
      ];
    } else {
      // Default to basic grammar for other languages
      return [
        {
          'title': 'Basic Sentence Structure',
          'explanation': 'How to form simple sentences.',
          'example': 'Subject + Verb + Object'
        },
        {
          'title': 'Basic Questions',
          'explanation': 'How to ask simple questions.',
          'example': 'Question word + Verb + Subject?'
        },
      ];
    }
  }
  
  // Get exercises for current level and language
  Future<List<Map<String, dynamic>>> getExercises() async {
    // This would be replaced with actual API call or database query
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Sample exercises
    if (_selectedLearningLanguage == 'English') {
      return [
        {
          'type': 'multiple_choice',
          'question': 'What is the translation of "Hello"?',
          'options': ['مرحبا', 'وداعا', 'شكرا', 'من فضلك'],
          'correct_answer': 'مرحبا'
        },
        {
          'type': 'fill_blank',
          'question': 'Complete the sentence: "I ___ to school every day."',
          'options': ['go', 'goes', 'going', 'went'],
          'correct_answer': 'go'
        },
        {
          'type': 'matching',
          'pairs': [
            {'word': 'Hello', 'translation': 'مرحبا'},
            {'word': 'Goodbye', 'translation': 'وداعا'},
            {'word': 'Thank you', 'translation': 'شكرا لك'},
            {'word': 'Please', 'translation': 'من فضلك'}
          ]
        },
        {
          'type': 'listening',
          'audio_url': 'path_to_audio/hello.mp3',
          'question': 'What did you hear?',
          'options': ['Hello', 'Goodbye', 'Thank you', 'Please'],
          'correct_answer': 'Hello'
        },
        {
          'type': 'speaking',
          'phrase_to_speak': 'Hello, how are you?',
          'translation': 'مرحبا، كيف حالك؟'
        }
      ];
    } else {
      // Default exercises for other languages
      return [
        {
          'type': 'multiple_choice',
          'question': 'Basic vocabulary question',
          'options': ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
          'correct_answer': 'Option 1'
        },
        {
          'type': 'fill_blank',
          'question': 'Basic fill in the blank question',
          'options': ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
          'correct_answer': 'Option 1'
        }
      ];
    }
  }
  
  // Get cultural content for current language
  Future<List<Map<String, String>>> getCulturalContent() async {
    // This would be replaced with actual API call or database query
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Sample cultural content
    if (_selectedLearningLanguage == 'English') {
      return [
        {
          'title': 'Greetings in English-speaking Countries',
          'content': 'In English-speaking countries, people often greet each other with a handshake in formal situations, and a hug or kiss on the cheek in informal situations with friends and family.',
          'image_url': 'path_to_image/greetings.jpg'
        },
        {
          'title': 'Tea Time in Britain',
          'content': 'Afternoon tea is a British tradition that involves drinking tea and eating small sandwiches and cakes in the late afternoon.',
          'image_url': 'path_to_image/tea_time.jpg'
        }
      ];
    } else if (_selectedLearningLanguage == 'Français') {
      return [
        {
          'title': 'La Bise (Cheek Kissing)',
          'content': 'In France, people often greet each other with "la bise" - kisses on the cheek. The number of kisses varies by region, from one to four.',
          'image_url': 'path_to_image/la_bise.jpg'
        },
        {
          'title': 'French Cuisine',
          'content': 'French cuisine is famous worldwide for its sophistication and diversity. Each region has its own specialties.',
          'image_url': 'path_to_image/french_cuisine.jpg'
        }
      ];
    } else {
      // Default cultural content for other languages
      return [
        {
          'title': 'Cultural Note 1',
          'content': 'Basic cultural information about the language and its speakers.',
          'image_url': 'path_to_image/default.jpg'
        },
        {
          'title': 'Cultural Note 2',
          'content': 'More cultural information about traditions and customs.',
          'image_url': 'path_to_image/default2.jpg'
        }
      ];
    }
  }
}
