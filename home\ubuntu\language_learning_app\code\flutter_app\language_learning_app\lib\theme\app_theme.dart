import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Colors
  static const Color primaryColor = Color(0xFF58CC02);
  static const Color secondaryColor = Color(0xFF1CB0F6);
  static const Color accentColor = Color(0xFFFF9600);
  static const Color errorColor = Color(0xFFFF4B4B);
  static const Color successColor = Color(0xFF78C800);
  static const Color backgroundColor = Color(0xFFFFFFFF);
  static const Color backgroundSecondaryColor = Color(0xFFF7F7F7);
  static const Color textPrimaryColor = Color(0xFF4B4B4B);
  static const Color textSecondaryColor = Color(0xFF777777);

  // Text Styles
  static TextStyle get headingLarge => const TextStyle(
        fontFamily: 'DinRound',
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: textPrimaryColor,
      );

  static TextStyle get headingMedium => const TextStyle(
        fontFamily: 'DinRound',
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: textPrimaryColor,
      );

  static TextStyle get headingSmall => const TextStyle(
        fontFamily: 'DinRound',
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: textPrimaryColor,
      );

  static TextStyle get bodyLarge => const TextStyle(
        fontFamily: 'DinRound',
        fontSize: 18,
        color: textPrimaryColor,
      );

  static TextStyle get bodyMedium => const TextStyle(
        fontFamily: 'DinRound',
        fontSize: 16,
        color: textPrimaryColor,
      );

  static TextStyle get bodySmall => const TextStyle(
        fontFamily: 'DinRound',
        fontSize: 14,
        color: textSecondaryColor,
      );

  static TextStyle get buttonText => const TextStyle(
        fontFamily: 'DinRound',
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      );

  // Theme Data
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: primaryColor,
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        error: errorColor,
        background: backgroundColor,
        surface: backgroundColor,
      ),
      scaffoldBackgroundColor: backgroundColor,
      appBarTheme: const AppBarTheme(
        backgroundColor: backgroundColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: IconThemeData(color: textPrimaryColor),
        titleTextStyle: TextStyle(
          fontFamily: 'DinRound',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: textPrimaryColor,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          textStyle: buttonText,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
          elevation: 2,
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor, width: 1.5),
          textStyle: buttonText.copyWith(color: primaryColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: secondaryColor,
          textStyle: bodyMedium.copyWith(color: secondaryColor),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: backgroundSecondaryColor,
        contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: secondaryColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 1.5),
        ),
        hintStyle: bodyMedium.copyWith(color: textSecondaryColor.withOpacity(0.7)),
      ),
      cardTheme: CardTheme(
        color: backgroundColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      ),
      dividerTheme: const DividerThemeData(
        color: backgroundSecondaryColor,
        thickness: 1,
        space: 1,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: backgroundColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: TextStyle(
          fontFamily: 'DinRound',
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: 'DinRound',
          fontSize: 12,
        ),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return Colors.white;
          }
          return Colors.white;
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor;
          }
          return textSecondaryColor.withOpacity(0.3);
        }),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor;
          }
          return Colors.transparent;
        }),
        side: const BorderSide(color: textSecondaryColor, width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor;
          }
          return textSecondaryColor;
        }),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: primaryColor,
        linearTrackColor: backgroundSecondaryColor,
        circularTrackColor: backgroundSecondaryColor,
      ),
      sliderTheme: SliderThemeData(
        activeTrackColor: secondaryColor,
        inactiveTrackColor: backgroundSecondaryColor,
        thumbColor: secondaryColor,
        overlayColor: secondaryColor.withOpacity(0.2),
        trackHeight: 4,
        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
        overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
      ),
      tabBarTheme: TabBarTheme(
        labelColor: secondaryColor,
        unselectedLabelColor: textSecondaryColor,
        indicatorColor: secondaryColor,
        labelStyle: bodyMedium.copyWith(fontWeight: FontWeight.bold),
        unselectedLabelStyle: bodyMedium,
      ),
      textTheme: TextTheme(
        displayLarge: headingLarge,
        displayMedium: headingMedium,
        displaySmall: headingSmall,
        bodyLarge: bodyLarge,
        bodyMedium: bodyMedium,
        bodySmall: bodySmall,
        labelLarge: buttonText,
      ),
      fontFamily: 'DinRound',
    );
  }

  // Fallback to Nunito font if DinRound is not available
  static ThemeData getFallbackTheme() {
    final baseTheme = lightTheme;
    final nunitoTextTheme = GoogleFonts.nunitoTextTheme(baseTheme.textTheme);
    
    return baseTheme.copyWith(
      textTheme: nunitoTextTheme,
      fontFamily: 'Nunito',
    );
  }
}
