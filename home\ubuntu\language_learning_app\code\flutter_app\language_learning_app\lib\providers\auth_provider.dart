import 'package:flutter/material.dart';

class AuthProvider extends ChangeNotifier {
  bool _isAuthenticated = false;
  String? _userId;
  String? _userEmail;
  String? _userName;
  String? _userProfileImage;

  bool get isAuthenticated => _isAuthenticated;
  String? get userId => _userId;
  String? get userEmail => _userEmail;
  String? get userName => _userName;
  String? get userProfileImage => _userProfileImage;

  Future<bool> signIn(String email, String password) async {
    try {
      // This would be replaced with actual authentication logic
      // For now, we'll simulate a successful login
      await Future.delayed(const Duration(seconds: 1));
      
      _isAuthenticated = true;
      _userId = "user_123";
      _userEmail = email;
      _userName = "مستخدم جديد";
      _userProfileImage = null;
      
      notifyListeners();
      return true;
    } catch (e) {
      print("Error signing in: $e");
      return false;
    }
  }

  Future<bool> signUp(String email, String password, String name) async {
    try {
      // This would be replaced with actual registration logic
      await Future.delayed(const Duration(seconds: 1));
      
      _isAuthenticated = true;
      _userId = "user_123";
      _userEmail = email;
      _userName = name;
      _userProfileImage = null;
      
      notifyListeners();
      return true;
    } catch (e) {
      print("Error signing up: $e");
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      // This would be replaced with actual sign out logic
      await Future.delayed(const Duration(milliseconds: 500));
      
      _isAuthenticated = false;
      _userId = null;
      _userEmail = null;
      _userName = null;
      _userProfileImage = null;
      
      notifyListeners();
    } catch (e) {
      print("Error signing out: $e");
    }
  }

  Future<bool> updateProfile({String? name, String? profileImage}) async {
    try {
      // This would be replaced with actual profile update logic
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (name != null) {
        _userName = name;
      }
      
      if (profileImage != null) {
        _userProfileImage = profileImage;
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      print("Error updating profile: $e");
      return false;
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      // This would be replaced with actual password reset logic
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      print("Error resetting password: $e");
      return false;
    }
  }
}
