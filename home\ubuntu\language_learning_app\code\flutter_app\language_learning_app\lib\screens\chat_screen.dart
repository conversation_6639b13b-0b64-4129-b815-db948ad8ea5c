import 'package:flutter/material.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:language_learning_app/providers/auth_provider.dart';
import 'package:language_learning_app/theme/app_theme.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final List<types.Message> _messages = [];
  late types.User _user;

  @override
  void initState() {
    super.initState();
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    _user = types.User(
      id: authProvider.userId ?? 'default_user_id',
      firstName: authProvider.userName ?? 'User',
      imageUrl: authProvider.userProfileImage,
    );
    _loadInitialMessages();
  }

  void _loadInitialMessages() {
    // Placeholder for loading initial messages from a backend or local storage
    // For now, add a welcome message
    final welcomeMessage = types.TextMessage(
      author: const types.User(id: 'system', firstName: 'System'),
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      text: 'مرحباً بك في الدردشة الجماعية! ابدأ بالتحدث مع المتعلمين الآخرين.',
    );
    setState(() {
      _messages.insert(0, welcomeMessage);
    });
  }

  void _handleSendPressed(types.PartialText message) {
    final textMessage = types.TextMessage(
      author: _user,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: const Uuid().v4(),
      text: message.text,
    );

    _addMessage(textMessage);

    // Placeholder for sending the message to a backend service
    // Simulate a response from another user or AI tutor
    _simulateResponse(message.text);
  }

  void _addMessage(types.Message message) {
    setState(() {
      _messages.insert(0, message);
    });
  }

  void _simulateResponse(String userMessage) {
    // Simulate a delay for the response
    Future.delayed(const Duration(seconds: 1), () {
      String responseText;
      if (userMessage.toLowerCase().contains('hello') || userMessage.contains('مرحبا')) {
        responseText = 'Hello there! How is your learning going?';
      } else if (userMessage.toLowerCase().contains('help') || userMessage.contains('مساعدة')) {
        responseText = 'Sure, I can help! What do you need assistance with?';
      } else {
        responseText = 'That\'s interesting! Tell me more.';
      }

      final aiMessage = types.TextMessage(
        author: const types.User(id: 'ai_tutor', firstName: 'AI Tutor', imageUrl: 'assets/images/ai_avatar.png'),
        createdAt: DateTime.now().millisecondsSinceEpoch,
        id: const Uuid().v4(),
        text: responseText,
      );
      _addMessage(aiMessage);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('دردشة جماعية'),
      ),
      body: Chat(
        messages: _messages,
        onSendPressed: _handleSendPressed,
        user: _user,
        theme: DefaultChatTheme(
          primaryColor: AppTheme.primaryColor,
          secondaryColor: AppTheme.backgroundSecondaryColor,
          inputBackgroundColor: Colors.white,
          inputTextColor: AppTheme.textPrimaryColor,
          messageInsetsVertical: 12.0,
          messageInsetsHorizontal: 16.0,
          sentMessageBodyTextStyle: AppTheme.bodyMedium.copyWith(color: Colors.white),
          receivedMessageBodyTextStyle: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimaryColor),
          inputTextStyle: AppTheme.bodyMedium,
          inputContainerDecoration: BoxDecoration(
            border: Border(top: BorderSide(color: Colors.grey[300]!)),
            color: Colors.white,
          ),
          attachmentButtonIcon: const Icon(Icons.add, color: AppTheme.primaryColor),
          sendButtonIcon: const Icon(Icons.send, color: AppTheme.primaryColor),
        ),
        l10n: const ChatL10nEn(
          inputPlaceholder: 'اكتب رسالتك هنا...',
          // Add other localizations if needed
        ),
        showUserAvatars: true,
        showUserNames: true,
      ),
    );
  }
}
