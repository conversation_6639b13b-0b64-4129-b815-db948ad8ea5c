import 'package:language_learning_app/models/user.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  User? _currentUser;
  bool _isAuthenticated = false;

  User? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;

  Future<bool> login(String email, String password) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock user data
      _currentUser = User(
        id: '1',
        email: email,
        name: 'Test User',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        learningLanguages: ['ar', 'fr'],
        nativeLanguage: 'en',
        level: 1,
        points: 0,
      );
      
      _isAuthenticated = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> signup(String email, String password, String name) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      _currentUser = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        email: email,
        name: name,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        learningLanguages: [],
        nativeLanguage: 'en',
        level: 1,
        points: 0,
      );
      
      _isAuthenticated = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    _isAuthenticated = false;
  }

  Future<bool> resetPassword(String email) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }
}
