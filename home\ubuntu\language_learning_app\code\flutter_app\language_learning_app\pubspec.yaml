name: language_learning_app
description: A language learning app with AI features and group chat.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  provider: ^6.0.5
  http: ^1.1.0
  shared_preferences: ^2.2.0
  sqflite: ^2.3.0
  path: ^1.8.3
  flutter_tts: ^3.8.3
  speech_to_text: ^6.3.0
  firebase_core: ^2.15.1
  firebase_auth: ^4.7.3
  cloud_firestore: ^4.8.5
  firebase_storage: ^11.2.6
  flutter_svg: ^2.0.7
  cached_network_image: ^3.2.3
  audioplayers: ^5.1.0
  flutter_markdown: ^0.6.17
  intl: ^0.18.1
  google_fonts: ^5.1.0
  flutter_dotenv: ^5.1.0
  lottie: ^2.6.0
  connectivity_plus: ^4.0.2
  flutter_sound: ^9.2.13
  permission_handler: ^10.4.3
  image_picker: ^1.0.4
  flutter_chat_ui: ^1.6.9
  uuid: ^3.0.7
  dio: ^5.3.2
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  get_it: ^7.6.0
  flutter_native_splash: ^2.3.2
  flutter_launcher_icons: ^0.13.1
  confetti: ^0.7.0
  flutter_animate: ^4.2.0
  flutter_staggered_animations: ^1.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.2
  build_runner: ^2.4.6
  mockito: ^5.4.2
  flutter_driver:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/audio/
    - assets/fonts/
    
  fonts:
    - family: DinRound
      fonts:
        - asset: assets/fonts/DINRoundPro.ttf
        - asset: assets/fonts/DINRoundPro-Bold.ttf
          weight: 700
        - asset: assets/fonts/DINRoundPro-Light.ttf
          weight: 300
