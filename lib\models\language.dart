class Language {
  final String code;
  final String name;
  final String nativeName;
  final String flagEmoji;
  final bool isSupported;

  Language({
    required this.code,
    required this.name,
    required this.nativeName,
    required this.flagEmoji,
    this.isSupported = true,
  });

  factory Language.fromJson(Map<String, dynamic> json) {
    return Language(
      code: json['code'] ?? '',
      name: json['name'] ?? '',
      nativeName: json['nativeName'] ?? '',
      flagEmoji: json['flagEmoji'] ?? '',
      isSupported: json['isSupported'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'nativeName': nativeName,
      'flagEmoji': flagEmoji,
      'isSupported': isSupported,
    };
  }

  static List<Language> getSupportedLanguages() {
    return [
      Language(
        code: 'en',
        name: 'English',
        nativeName: 'English',
        flagEmoji: '🇺🇸',
      ),
      Language(
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        flagEmoji: '🇸🇦',
      ),
      Language(
        code: 'fr',
        name: 'French',
        nativeName: 'Français',
        flagEmoji: '🇫🇷',
      ),
      Language(
        code: 'es',
        name: 'Spanish',
        nativeName: 'Español',
        flagEmoji: '🇪🇸',
      ),
      Language(
        code: 'de',
        name: 'German',
        nativeName: 'Deutsch',
        flagEmoji: '🇩🇪',
      ),
    ];
  }
}
