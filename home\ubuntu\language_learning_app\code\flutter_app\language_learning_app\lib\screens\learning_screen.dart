import 'package:flutter/material.dart';
import 'package:language_learning_app/providers/language_provider.dart';
import 'package:language_learning_app/providers/progress_provider.dart';
import 'package:language_learning_app/theme/app_theme.dart';
import 'package:language_learning_app/widgets/custom_button.dart';
import 'package:provider/provider.dart';

class LearningScreen extends StatefulWidget {
  const LearningScreen({super.key});

  @override
  State<LearningScreen> createState() => _LearningScreenState();
}

class _LearningScreenState extends State<LearningScreen> {
  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final progressProvider = Provider.of<ProgressProvider>(context);

    // Placeholder data for learning path
    final List<Map<String, dynamic>> learningPath = [
      {'title': 'أساسيات 1', 'icon': Icons.looks_one, 'is_unlocked': true, 'progress': 0.8},
      {'title': 'عبارات شائعة', 'icon': Icons.chat_bubble_outline, 'is_unlocked': true, 'progress': 0.5},
      {'title': 'الطعام', 'icon': Icons.restaurant_menu, 'is_unlocked': false, 'progress': 0.0},
      {'title': 'الحيوانات', 'icon': Icons.pets, 'is_unlocked': false, 'progress': 0.0},
      {'title': 'الملابس', 'icon': Icons.checkroom, 'is_unlocked': false, 'progress': 0.0},
      {'title': 'أساسيات 2', 'icon': Icons.looks_two, 'is_unlocked': false, 'progress': 0.0},
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text('تعلم ${languageProvider.selectedLearningLanguage}'),
        actions: [
          // Hearts indicator
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                Icon(Icons.favorite, color: AppTheme.errorColor, size: 20),
                const SizedBox(width: 4),
                Text(
                  '${progressProvider.hearts}',
                  style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          // Streak indicator
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                Icon(Icons.local_fire_department, color: AppTheme.accentColor, size: 20),
                const SizedBox(width: 4),
                Text(
                  '${progressProvider.streak}',
                  style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: learningPath.length,
        itemBuilder: (context, index) {
          final module = learningPath[index];
          return _buildLearningModule(context, module);
        },
      ),
    );
  }

  Widget _buildLearningModule(BuildContext context, Map<String, dynamic> module) {
    final bool isUnlocked = module['is_unlocked'] ?? false;
    final double progress = module['progress'] ?? 0.0;

    return Opacity(
      opacity: isUnlocked ? 1.0 : 0.5,
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        elevation: isUnlocked ? 2 : 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: isUnlocked
              ? () {
                  // Navigate to lesson screen for this module
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('بدء الدرس: ${module['title']}'),
                      duration: const Duration(seconds: 1),
                    ),
                  );
                }
              : null,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Module Icon with Progress Ring
                SizedBox(
                  width: 60,
                  height: 60,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CircularProgressIndicator(
                        value: progress,
                        strokeWidth: 6,
                        backgroundColor: AppTheme.backgroundSecondaryColor,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          isUnlocked ? AppTheme.primaryColor : Colors.grey,
                        ),
                      ),
                      Icon(
                        isUnlocked ? module['icon'] : Icons.lock_outline,
                        size: 30,
                        color: isUnlocked ? AppTheme.primaryColor : Colors.grey,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // Module Title
                Expanded(
                  child: Text(
                    module['title'],
                    style: AppTheme.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isUnlocked ? AppTheme.textPrimaryColor : Colors.grey,
                    ),
                  ),
                ),
                // Start Button (if unlocked)
                if (isUnlocked)
                  Icon(
                    Icons.play_circle_fill,
                    color: AppTheme.primaryColor,
                    size: 30,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
